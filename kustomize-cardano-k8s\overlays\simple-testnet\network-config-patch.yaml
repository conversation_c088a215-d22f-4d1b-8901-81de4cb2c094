# Patch to configure Cardano Node for testnet
apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-node-config
data:
  # Testnet topology configuration
  topology.json: |
    {
      "localRoots": [
        {
          "accessPoints": [],
          "advertise": false,
          "valency": 1
        }
      ],
      "publicRoots": [
        {
          "accessPoints": [
            {
              "address": "relays-new.cardano-testnet.iohkdev.io",
              "port": 3001
            }
          ],
          "advertise": false
        }
      ],
      "useLedgerAfterSlot": -1
    }
  
  # Testnet node configuration
  config.json: |
    {
      "ApplicationName": "cardano-sl",
      "ApplicationVersion": 1,
      "Protocol": "Cardano",
      "RequiresNetworkMagic": "RequiresMagic",
      "EnableLogMetrics": true,
      "EnableLogging": true,
      "TurnOnLogMetrics": true,
      "TurnOnLogging": true,
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {
          "cardano.node.metrics": [
            "EKGViewBK"
          ]
        }
      },
      "setupBackends": [
        "KatipBK",
        "EKGViewBK"
      ],
      "setupScribes": [
        {
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }

---
# Patch to configure DB Sync for testnet
apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-db-sync-config
data:
  db-sync-config.json: |
    {
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "NetworkName": "testnet",
      "PrometheusPort": 8080,
      "RequiresNetworkMagic": "RequiresMagic",
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {},
        "mapSeverity": {
          "db-sync-node": "Info"
        }
      },
      "setupBackends": [
        "KatipBK"
      ],
      "setupScribes": [
        {
          "scFormat": "ScText",
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
