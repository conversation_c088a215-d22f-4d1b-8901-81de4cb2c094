apiVersion: v1
kind: ConfigMap
metadata:
  name: cardano-db-sync-config
  labels:
    app.kubernetes.io/component: cardano-db-sync
    app.kubernetes.io/part-of: cardano-stack
data:
  # Simplified entrypoint script for educational purposes
  entrypoint.sh: |
    #!/bin/bash
    set -e
    
    echo "Starting Cardano DB Sync for network: ${NETWORK}"
    echo "Connecting to PostgreSQL at: ${POSTGRES_HOST}:${POSTGRES_PORT}"
    echo "Database: ${POSTGRES_DB}"
    echo "Socket path: ${CARDANO_NODE_SOCKET_PATH}"
    
    # Wait for PostgreSQL to be ready
    echo "Waiting for PostgreSQL to be ready..."
    until pg_isready -h "${POSTGRES_HOST}" -p "${POSTGRES_PORT}" -U "${POSTGRES_USER}"; do
      echo "PostgreSQL is not ready yet, waiting..."
      sleep 5
    done
    echo "PostgreSQL is ready!"
    
    # Wait for cardano-node socket to be available
    echo "Waiting for Cardano Node socket..."
    while [ ! -S "${CARDANO_NODE_SOCKET_PATH}" ]; do
      echo "Cardano Node socket not available yet, waiting..."
      sleep 10
    done
    echo "Cardano Node socket is available!"
    
    # Start cardano-db-sync
    exec cardano-db-sync \
      --config /config/db-sync-config.json \
      --socket-path "${CARDANO_NODE_SOCKET_PATH}" \
      --state-dir /var/lib/cexplorer \
      --schema-dir /opt/cardano/db-sync/schema/
  
  # Simplified DB Sync configuration
  db-sync-config.json: |
    {
      "EnableLogMetrics": false,
      "EnableLogging": true,
      "NetworkName": "mainnet",
      "PrometheusPort": 8080,
      "RequiresNetworkMagic": "RequiresNoMagic",
      "defaultBackends": [
        "KatipBK"
      ],
      "defaultScribes": [
        [
          "StdoutSK",
          "stdout"
        ]
      ],
      "minSeverity": "Info",
      "options": {
        "mapBackends": {},
        "mapSeverity": {
          "db-sync-node": "Info"
        }
      },
      "setupBackends": [
        "KatipBK"
      ],
      "setupScribes": [
        {
          "scFormat": "ScText",
          "scKind": "StdoutSK",
          "scName": "stdout",
          "scRotation": null
        }
      ]
    }
  
  # Health check script
  healthcheck.sh: |
    #!/bin/bash
    # Simple health check - verify the process is running and database connection works
    if ! pgrep -f cardano-db-sync > /dev/null; then
      echo "cardano-db-sync process not found"
      exit 1
    fi
    
    # Check if we can connect to the database
    if ! pg_isready -h "${POSTGRES_HOST}" -p "${POSTGRES_PORT}" -U "${POSTGRES_USER}" > /dev/null 2>&1; then
      echo "Cannot connect to PostgreSQL"
      exit 1
    fi
    
    echo "Health check passed"
    exit 0
