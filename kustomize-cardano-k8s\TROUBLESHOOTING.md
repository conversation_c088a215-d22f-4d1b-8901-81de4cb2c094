# Troubleshooting Guide

This guide helps you diagnose and fix common issues with the Cardano Kubernetes deployment.

## Quick Diagnostics

### Check Overall Status
```bash
# Check all pods
kubectl get pods -n cardano-testnet

# Check services
kubectl get svc -n cardano-testnet

# Check persistent volume claims
kubectl get pvc -n cardano-testnet

# Check events
kubectl get events -n cardano-testnet --sort-by='.lastTimestamp'
```

## Common Issues

### 1. Pods Stuck in Pending State

**Symptoms**: Pods show `Pending` status for extended periods

**Diagnosis**:
```bash
kubectl describe pod <pod-name> -n cardano-testnet
```

**Common Causes & Solutions**:

- **Insufficient Resources**:
  ```bash
  # Check node resources
  kubectl describe nodes
  kubectl top nodes
  ```
  *Solution*: Scale cluster or reduce resource requests

- **Storage Issues**:
  ```bash
  # Check PVC status
  kubectl get pvc -n cardano-testnet
  kubectl describe pvc <pvc-name> -n cardano-testnet
  ```
  *Solution*: Ensure storage class is available and has capacity

- **Node Selector Issues**:
  ```bash
  # Check node labels
  kubectl get nodes --show-labels
  ```
  *Solution*: Remove node selectors or label nodes appropriately

### 2. Cardano Node Issues

**Symptoms**: Node fails to start or sync

**Diagnosis**:
```bash
kubectl logs statefulset/cardano-node -n cardano-testnet --tail=100
```

**Common Issues**:

- **Configuration Errors**:
  ```bash
  # Check config map
  kubectl get configmap cardano-node-config -n cardano-testnet -o yaml
  ```
  *Solution*: Verify topology.json and config.json are valid

- **Network Connectivity**:
  ```bash
  # Test from inside pod
  kubectl exec -it cardano-node-0 -n cardano-testnet -- nslookup relays-new.cardano-testnet.iohkdev.io
  ```
  *Solution*: Check firewall rules and DNS resolution

- **Storage Full**:
  ```bash
  # Check disk usage
  kubectl exec -it cardano-node-0 -n cardano-testnet -- df -h
  ```
  *Solution*: Increase PVC size or clean up old data

### 3. PostgreSQL Issues

**Symptoms**: Database connection failures

**Diagnosis**:
```bash
kubectl logs statefulset/postgres -n cardano-testnet --tail=100
```

**Common Issues**:

- **Initialization Problems**:
  ```bash
  # Check if database initialized
  kubectl exec -it postgres-0 -n cardano-testnet -- ls -la /var/lib/postgresql/data/
  ```
  *Solution*: Delete PVC and restart if corrupted

- **Connection Issues**:
  ```bash
  # Test connection
  kubectl exec -it postgres-0 -n cardano-testnet -- pg_isready -U postgres
  ```
  *Solution*: Check service configuration and network policies

- **Resource Constraints**:
  ```bash
  # Check memory usage
  kubectl top pod postgres-0 -n cardano-testnet
  ```
  *Solution*: Increase memory limits

### 4. DB Sync Issues

**Symptoms**: DB Sync fails to connect or sync

**Diagnosis**:
```bash
kubectl logs statefulset/cardano-db-sync -n cardano-testnet --tail=100
```

**Common Issues**:

- **Socket Connection**:
  ```bash
  # Check if node socket exists
  kubectl exec -it cardano-db-sync-0 -n cardano-testnet -- ls -la /node-ipc/
  ```
  *Solution*: Ensure cardano-node is running and socket is created

- **Database Connection**:
  ```bash
  # Test database connection
  kubectl exec -it cardano-db-sync-0 -n cardano-testnet -- pg_isready -h postgres -U postgres
  ```
  *Solution*: Verify PostgreSQL is running and credentials are correct

- **Schema Issues**:
  ```bash
  # Check database tables
  kubectl exec -it postgres-0 -n cardano-testnet -- psql -U postgres -d cexplorer -c '\dt'
  ```
  *Solution*: Ensure DB Sync has proper permissions and schema

## Performance Issues

### Slow Sync Performance

**Cardano Node**:
```bash
# Check sync progress
kubectl exec -it cardano-node-0 -n cardano-testnet -- curl -s localhost:12788 | jq '.cardano.node.metrics.blockNum.int.val'
```

**DB Sync**:
```bash
# Check sync progress
kubectl logs statefulset/cardano-db-sync -n cardano-testnet | grep "Progress"
```

**Optimization**:
- Increase CPU/memory resources
- Use faster storage (SSD)
- Optimize PostgreSQL settings

### Resource Monitoring

```bash
# Monitor resource usage
kubectl top pods -n cardano-testnet

# Detailed resource usage
kubectl describe pod <pod-name> -n cardano-testnet
```

## Network Issues

### DNS Resolution
```bash
# Test DNS from pod
kubectl exec -it cardano-node-0 -n cardano-testnet -- nslookup kubernetes.default.svc.cluster.local
```

### Service Discovery
```bash
# Test service connectivity
kubectl exec -it cardano-db-sync-0 -n cardano-testnet -- nc -zv postgres 5432
```

### External Connectivity
```bash
# Test external connectivity
kubectl exec -it cardano-node-0 -n cardano-testnet -- curl -I https://google.com
```

## Data Recovery

### Backup Verification
```bash
# Check data integrity
kubectl exec -it postgres-0 -n cardano-testnet -- pg_dump -U postgres cexplorer > backup.sql
```

### Restore from Backup
```bash
# Restore database
kubectl exec -i postgres-0 -n cardano-testnet -- psql -U postgres cexplorer < backup.sql
```

## Emergency Procedures

### Force Pod Restart
```bash
# Delete pod (StatefulSet will recreate)
kubectl delete pod cardano-node-0 -n cardano-testnet
```

### Reset Deployment
```bash
# Delete and recreate
kubectl delete -k kustomize-cardano-k8s/overlays/simple-testnet/
kubectl apply -k kustomize-cardano-k8s/overlays/simple-testnet/
```

### Clean Slate (Data Loss)
```bash
# Delete everything including data
kubectl delete -k kustomize-cardano-k8s/overlays/simple-testnet/
kubectl delete pvc --all -n cardano-testnet
kubectl apply -k kustomize-cardano-k8s/overlays/simple-testnet/
```

## Getting Help

### Collect Diagnostics
```bash
# Create diagnostic bundle
kubectl get all -n cardano-testnet > diagnostics.txt
kubectl describe pods -n cardano-testnet >> diagnostics.txt
kubectl logs --all-containers=true -n cardano-testnet >> diagnostics.txt
```

### Useful Commands
```bash
# Watch pod status
kubectl get pods -n cardano-testnet -w

# Follow all logs
kubectl logs -f -l app.kubernetes.io/part-of=cardano-stack -n cardano-testnet

# Check resource quotas
kubectl describe resourcequota -n cardano-testnet
```
