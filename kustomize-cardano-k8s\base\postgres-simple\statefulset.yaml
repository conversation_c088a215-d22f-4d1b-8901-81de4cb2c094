apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres
  labels:
    app.kubernetes.io/component: postgres
    app.kubernetes.io/part-of: cardano-stack
spec:
  serviceName: postgres
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: postgres
      app.kubernetes.io/part-of: cardano-stack
  template:
    metadata:
      labels:
        app.kubernetes.io/component: postgres
        app.kubernetes.io/part-of: cardano-stack
    spec:
      containers:
      - name: postgres
        image: postgres:17.2-alpine
        ports:
        - containerPort: 5432
          name: postgres
        env:
        - name: POSTGRES_LOGGING
          value: "true"
        - name: POSTGRES_DB
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_DB
        - name: POSTGRES_USER
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: POSTGRES_USER
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: POSTGRES_PASSWORD
        # PostgreSQL optimization settings from docker-compose
        args:
        - -c
        - maintenance_work_mem=1GB
        - -c
        - max_parallel_maintenance_workers=4
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "2"
        # Health check equivalent to docker-compose
        livenessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
        readinessProbe:
          exec:
            command:
            - pg_isready
            - -U
            - postgres
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 5
  volumeClaimTemplates:
  - metadata:
      name: postgres-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 20Gi
