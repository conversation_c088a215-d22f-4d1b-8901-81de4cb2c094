# Architecture Overview

This document explains the architecture and design decisions for the simplified Cardano Kubernetes deployment.

## Design Principles

### Educational Focus
- **Simplified Configuration**: Removed complex features like snapshot restoration, advanced networking, and HA setups
- **Clear Documentation**: Every component is well-documented with educational comments
- **Sensible Defaults**: Pre-configured with working defaults that require minimal blockchain knowledge
- **Beginner-Friendly**: Uses standard Kubernetes patterns and official Docker images

### Production-Ready Foundation
- **Official Images**: Uses official Cardano images from `ghcr.io/intersectmbo/`
- **Health Checks**: Comprehensive health checks for all services
- **Resource Management**: Proper resource requests and limits
- **Persistent Storage**: All data is persisted across pod restarts
- **Security**: Follows Kubernetes security best practices

## Component Architecture

### Base Components

#### 1. Common Configuration (`base/common-simple/`)
- **Purpose**: Shared environment variables and configuration
- **Key Settings**: Database connection, network selection, socket paths
- **Pattern**: ConfigMap with environment-specific overrides in overlays

#### 2. PostgreSQL (`base/postgres-simple/`)
- **Purpose**: Database for blockchain data storage
- **Image**: `postgres:17.2-alpine`
- **Features**: 
  - Optimized PostgreSQL settings from docker-compose
  - Health checks with `pg_isready`
  - Persistent storage for database files
  - Secret-based password management

#### 3. Cardano Node (`base/cardano-node-simple/`)
- **Purpose**: Core Cardano blockchain node
- **Image**: `ghcr.io/intersectmbo/cardano-node:10.1.4`
- **Features**:
  - Simplified entrypoint script
  - EKG metrics endpoint for monitoring
  - Persistent storage for blockchain data
  - Network-specific configuration via overlays

#### 4. Cardano DB Sync (`base/cardano-db-sync-simple/`)
- **Purpose**: Synchronizes blockchain data to PostgreSQL
- **Image**: `ghcr.io/intersectmbo/cardano-db-sync:********`
- **Features**:
  - Waits for dependencies (PostgreSQL and Cardano Node)
  - Custom health checks
  - Prometheus metrics endpoint
  - Persistent state storage

### Overlay Architecture

#### Network-Specific Overlays
- **Testnet** (`overlays/simple-testnet/`): Configured for Cardano testnet
- **Mainnet** (`overlays/simple-mainnet/`): Configured for Cardano mainnet

#### Overlay Components
1. **Network Configuration**: Topology and node configuration patches
2. **Storage Sizing**: Environment-appropriate storage allocations
3. **Environment Variables**: Network-specific settings
4. **Namespace Isolation**: Separate namespaces for each environment

## Data Flow

```
Internet → Cardano Node → Unix Socket → DB Sync → PostgreSQL
                ↓
            EKG Metrics (Port 12788)
                                        ↓
                                 Prometheus Metrics (Port 8080)
```

### Communication Patterns

1. **Cardano Node ↔ Network**: P2P communication on port 3001
2. **Node ↔ DB Sync**: Unix socket communication via shared volume
3. **DB Sync ↔ PostgreSQL**: TCP connection on port 5432
4. **Monitoring**: HTTP endpoints for metrics collection

## Storage Architecture

### Persistent Volumes
- **Cardano Node**: Blockchain database (`/data/db`)
- **DB Sync**: Application state (`/var/lib/cexplorer`)
- **PostgreSQL**: Database files (`/var/lib/postgresql/data`)

### Shared Volumes
- **IPC Socket**: EmptyDir volume for Unix socket communication

### Storage Sizing
- **Testnet**: ~60GB total storage
- **Mainnet**: ~350GB total storage

## Security Model

### Network Security
- **Namespace Isolation**: Each environment in separate namespace
- **Service-to-Service**: ClusterIP services for internal communication
- **External Access**: No direct external exposure (use port-forward for access)

### Credential Management
- **PostgreSQL Password**: Kubernetes Secret (base64 encoded)
- **Service Accounts**: Default service accounts (can be customized)

### Container Security
- **Non-Root**: Containers run as non-root where possible
- **Resource Limits**: CPU and memory limits prevent resource exhaustion
- **Health Checks**: Proactive health monitoring

## Monitoring and Observability

### Health Checks
- **PostgreSQL**: `pg_isready` command
- **Cardano Node**: EKG endpoint availability
- **DB Sync**: Custom script checking process and database connectivity

### Metrics Endpoints
- **Cardano Node**: EKG metrics on port 12788
- **DB Sync**: Prometheus metrics on port 8080

### Logging
- **Standard Output**: All services log to stdout/stderr
- **Kubernetes Integration**: Logs available via `kubectl logs`

## Scalability Considerations

### Current Limitations
- **Single Replica**: All services run as single replicas
- **Shared Storage**: Uses ReadWriteOnce volumes
- **No Load Balancing**: Direct service-to-service communication

### Future Enhancements
- **Read Replicas**: PostgreSQL read replicas for query scaling
- **Multiple Nodes**: Multiple Cardano nodes for redundancy
- **Horizontal Scaling**: Scale DB Sync for different use cases

## Comparison with Production Setup

### Simplified vs. Production
| Feature | Simplified | Production (kustomize-dandelion) |
|---------|------------|----------------------------------|
| Images | Official | Custom with optimizations |
| Snapshot Restore | No | Yes (automated) |
| HA Database | No | PostgreSQL HA cluster |
| Networking | Basic | Advanced with socat/chisel |
| Monitoring | Basic | Full observability stack |
| Security | Standard | Enhanced with custom patches |

### Migration Path
This simplified setup provides a foundation that can be enhanced:
1. Add snapshot restoration for faster sync
2. Implement HA PostgreSQL
3. Add advanced monitoring
4. Implement backup strategies
5. Add network policies and security hardening
