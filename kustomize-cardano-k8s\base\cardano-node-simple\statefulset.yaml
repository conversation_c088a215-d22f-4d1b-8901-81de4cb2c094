apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: cardano-node
  labels:
    app.kubernetes.io/component: cardano-node
    app.kubernetes.io/part-of: cardano-stack
spec:
  serviceName: cardano-node
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/component: cardano-node
      app.kubernetes.io/part-of: cardano-stack
  template:
    metadata:
      labels:
        app.kubernetes.io/component: cardano-node
        app.kubernetes.io/part-of: cardano-stack
    spec:
      initContainers:
      # Clean up any lost+found directories that might interfere
      - name: cleanup-volumes
        image: busybox:1.36
        command: [ "sh", "-c", "rm -rf /data/db/lost+found /ipc/lost+found || true" ]
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: node-ipc
          mountPath: /ipc
      containers:
      - name: cardano-node
        image: ghcr.io/intersectmbo/cardano-node:10.1.4
        env:
        - name: NETWORK
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: NETWORK
        - name: CARDANO_NODE_SOCKET_PATH
          valueFrom:
            configMapKeyRef:
              name: common-env
              key: CARDANO_NODE_SOCKET_PATH
        ports:
        - containerPort: 3001
          name: cardano-node
          protocol: TCP
        - containerPort: 12788
          name: ekg-metrics
          protocol: TCP
        volumeMounts:
        - name: node-db
          mountPath: /data/db
        - name: node-ipc
          mountPath: /ipc
        - name: cardano-node-config
          mountPath: /config
        - name: cardano-entrypoint-script
          mountPath: /scripts
          subPath: entrypoint.sh
        resources:
          requests:
            memory: "4Gi"
            cpu: "1"
          limits:
            memory: "8Gi"
            cpu: "4"
        # Health check equivalent to docker-compose EKG port check
        livenessProbe:
          httpGet:
            path: /
            port: 12788
          initialDelaySeconds: 300
          periodSeconds: 60
          timeoutSeconds: 10
          failureThreshold: 5
        readinessProbe:
          httpGet:
            path: /
            port: 12788
          initialDelaySeconds: 60
          periodSeconds: 60
          timeoutSeconds: 10
          failureThreshold: 3
      volumes:
      - name: node-ipc
        emptyDir: {}
      - name: cardano-node-config
        configMap:
          name: cardano-node-config
          defaultMode: 0755
      - name: cardano-entrypoint-script
        configMap:
          name: cardano-node-config
          defaultMode: 0755
  volumeClaimTemplates:
  - metadata:
      name: node-db
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 8Gi
