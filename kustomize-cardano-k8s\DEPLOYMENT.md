# Cardano Kubernetes Deployment Guide

This guide walks you through deploying Cardano Node and DB Sync services on Kubernetes using these simplified manifests.

## Prerequisites

1. **Kubernetes Cluster**: A running Kubernetes cluster with:
   - Persistent volume support (for data storage)
   - At least 8GB RAM and 4 CPU cores available
   - kubectl configured to access your cluster

2. **Storage Requirements**:
   - **Testnet**: ~60GB total (16GB node + 8GB db-sync + 30GB postgres + overhead)
   - **Mainnet**: ~350GB total (120GB node + 20GB db-sync + 200GB postgres + overhead)

3. **Network Access**: Outbound internet access for:
   - Downloading blockchain data
   - Connecting to Cardano network peers

## Quick Start

### Deploy to Testnet (Recommended for Learning)

```bash
# Deploy all services to testnet
kubectl apply -k kustomize-cardano-k8s/overlays/simple-testnet/

# Check deployment status
kubectl get pods -n cardano-testnet

# Follow logs
kubectl logs -f statefulset/cardano-node -n cardano-testnet
kubectl logs -f statefulset/postgres -n cardano-testnet
kubectl logs -f statefulset/cardano-db-sync -n cardano-testnet
```

### Deploy to Mainnet (Production)

```bash
# Deploy all services to mainnet
kubectl apply -k kustomize-cardano-k8s/overlays/simple-mainnet/

# Check deployment status
kubectl get pods -n cardano-mainnet

# Follow logs
kubectl logs -f statefulset/cardano-node -n cardano-mainnet
```

## Deployment Process

The services start in this order:

1. **PostgreSQL** - Database starts first
2. **Cardano Node** - Begins syncing blockchain data
3. **Cardano DB Sync** - Waits for both PostgreSQL and Cardano Node, then starts syncing

### Expected Startup Time

- **PostgreSQL**: ~30 seconds
- **Cardano Node**: 
  - Testnet: 2-6 hours for initial sync
  - Mainnet: 24-72 hours for initial sync
- **Cardano DB Sync**: Starts after node sync, adds 2-4 hours

## Monitoring Deployment

### Check Pod Status
```bash
# Testnet
kubectl get pods -n cardano-testnet -w

# Mainnet  
kubectl get pods -n cardano-mainnet -w
```

### View Logs
```bash
# Cardano Node logs
kubectl logs -f statefulset/cardano-node -n cardano-testnet

# DB Sync logs
kubectl logs -f statefulset/cardano-db-sync -n cardano-testnet

# PostgreSQL logs
kubectl logs -f statefulset/postgres -n cardano-testnet
```

### Check Storage Usage
```bash
# View persistent volume claims
kubectl get pvc -n cardano-testnet

# Describe storage details
kubectl describe pvc -n cardano-testnet
```

### Access Services

```bash
# Port forward to access PostgreSQL
kubectl port-forward svc/postgres 5432:5432 -n cardano-testnet

# Port forward to access Cardano Node metrics
kubectl port-forward svc/cardano-node 12788:12788 -n cardano-testnet

# Port forward to access DB Sync metrics
kubectl port-forward svc/cardano-db-sync 8080:8080 -n cardano-testnet
```

## Troubleshooting

### Common Issues

1. **Pods Stuck in Pending**: Check storage provisioning
   ```bash
   kubectl describe pvc -n cardano-testnet
   ```

2. **Cardano Node Won't Start**: Check configuration and logs
   ```bash
   kubectl logs statefulset/cardano-node -n cardano-testnet
   ```

3. **DB Sync Connection Issues**: Verify PostgreSQL is running
   ```bash
   kubectl get pods -n cardano-testnet
   kubectl logs statefulset/postgres -n cardano-testnet
   ```

### Health Checks

All services include health checks:
- **PostgreSQL**: `pg_isready` command
- **Cardano Node**: EKG metrics endpoint (port 12788)
- **DB Sync**: Custom health check script

### Resource Monitoring

Monitor resource usage:
```bash
# CPU and memory usage
kubectl top pods -n cardano-testnet

# Storage usage (if metrics-server is available)
kubectl top nodes
```

## Cleanup

To remove the deployment:

```bash
# Remove testnet deployment
kubectl delete -k kustomize-cardano-k8s/overlays/simple-testnet/

# Remove mainnet deployment  
kubectl delete -k kustomize-cardano-k8s/overlays/simple-mainnet/
```

**Note**: This will delete all data. Persistent volumes may need manual cleanup depending on your storage class configuration.

## Security Considerations

1. **Database Password**: Change the default PostgreSQL password in production
2. **Network Policies**: Consider implementing network policies to restrict traffic
3. **Resource Limits**: Adjust resource limits based on your cluster capacity
4. **Backup Strategy**: Implement regular backups of persistent volumes

## Next Steps

- Set up monitoring with Prometheus/Grafana
- Configure log aggregation
- Implement backup strategies
- Set up alerting for service health
